## USER_BACKEND ----------
NODE_ENV=
PORT=

DB_HOST=
DB_PORT=
DB_USERNAME=
DB_PASSWORD=
DB_NAME=

PUB_SUB_REDIS_DB_PASSWORD=
PUB_SUB_REDIS_DB_HOST=
PUB_SUB_REDIS_DB_PORT=

LOG_LEVEL=

# auth variables
APP_SECRET=
TOKEN_EXPIRE_TIME=
RES_TOKEN_EXPIRE_TIME=
LIVE_CASINO_TOKEN_EXPIRE_TIME=
## USER_BACKEND ----------


## USER_FRONTEND ----------
CI=
REACT_APP_BACKEND_PORT=
## USER_FRONTEND ----------


## ADMIN_APP ----------
APP_PORT=8002
RAILS_ENV=development
REDIS_URL=
RAILS_MAX_THREADS=1
ELASTICSEARCH_URL=

APP_TWILIO_ACCOUNT_SID=
APP_TWILIO_SERVICE_ID=
APP_TWILIO_AUTH_TOKEN=
APP_SENDGRID_RELAY_KEY=
APP_SENDGRID_HOST=
APP_SENDGRID_PORT=
APP_SENDGRID_USERNAME=
APP_SENDGRID_EMAIL=
APP_EZUGI_OPERATOR_ID=
APP_EZUGI_HASH_KEY=

SUPER_ADMIN_OWNER_PASSWORD=
SUPER_ADMIN_MANAGER_PASSWORD=

ADMIN_APP_SUBDOMAIN=
SUPER_ADMIN_DOMAIN=
## ADMIN_APP ----------
