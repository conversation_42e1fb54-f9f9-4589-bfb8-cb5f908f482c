version: "3.5"

networks:
  ezugi-main-platform:
    name: ezugi-main-platform
    external: true

services:
  redis-database:
    image: redis:5.0.7-alpine
    volumes:
      - ./docker_volumes_data/redis-database:/data
    ports:
      - "8000:6379"
    networks:
      - ezugi-main-platform

  database:
    image: postgres:12.1
    volumes:
      - ./docker_volumes_data/database:/var/lib/postgresql/data
    ports:
      - "8001:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: ezugimainplatform_staging
    networks:
      - ezugi-main-platform



  # admin-app:
  #   build:
  #     context: ../admin-app
  #   command: bundle exec rails s -p 8080 -b '0.0.0.0'
  #   stdin_open: true
  #   tty: true
  #   volumes:
  #     - ../admin-app:/ezugi
  #   ports:
  #     - "8002:8080"
  #     - "5630:9229"
  #   env_file: .env
  #   depends_on:
  #     - database
  #     - elasticsearch
  #   networks:
  #     - ezugi-main-platform

  user-backend:
    build:
      context: ../user-backend-api
      target: builder
    command: [ "npm", "run", "start:dev" ]
    volumes:
      - /home/<USER>/app/node_modules/
      - ../user-backend-api:/home/<USER>/app/
    ports:
      - "8003:8080"
      - "5631:9229"
    env_file: .env
    depends_on:
      - database
    # - elasticsearch
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - ezugi-main-platform
  # user-backend-sportbook:
  #   build:
  #     context: ../sports-aggregator-microservice
  #     target: builder
  #   command: ["npm", "run", "start:dev"]
  #   volumes:
  #     - /home/<USER>/app/node_modules/
  #     - ../sports-aggregator-microservice:/home/<USER>/app/
  #   ports:
  #     - "8002:8080"
  #     - "5630:9229"
  #   env_file: .env
  #   depends_on:
  #     - database
  #     - elasticsearch
  #   networks:
  #     - ezugi-main-platform
  user-frontend:
    build:
      context: ../user-frontend
      target: developmet
    command: [ "npm", "run", "start" ]
    volumes:
      - /home/<USER>/app/node_modules/
      - ../user-frontend:/home/<USER>/app/
    ports:
      - "8004:8080"
      - "5632:9229"
    env_file: .env
    depends_on:
      - user-backend
    networks:
      - ezugi-main-platform

  # pg-admin:
  #   image: dpage/pgadmin4:4.18
  #   restart: always
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: <EMAIL>
  #     PGADMIN_DEFAULT_PASSWORD: admin
  #     PGADMIN_LISTEN_PORT: 80
  #   ports:
  #     - "8005:80"
  #   volumes:
  #     - ./docker_volumes_data/pgadmin-data:/var/lib/pgadmin
  #   networks:
  #     - ezugi-main-platform

  nginx:
    image: nginx:1.19.6-alpine
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    restart: always
    ports:
      - 80:80
    environment:
      NGINX_PORT: 80
    depends_on:
      - user-frontend
      - user-backend
    networks:
      - ezugi-main-platform

  # elasticsearch:
  #   image: docker.elastic.co/elasticsearch/elasticsearch:7.10.2
  #   environment:
  #     - discovery.type=single-node
  #   ulimits:
  #     memlock:
  #       soft: -1
  #       hard: -1
  #   volumes:
  #     - ./docker_volumes_data/elastic_database:/usr/share/elasticsearch/data
  #   ports:
  #     - 9200:9200
  #     - 9300:9300
  #   networks:
  #     - ezugi-main-platform
  # sidekiq:
  #   depends_on:
  #     - "database"
  #     - "redis-database"
  #   build:
  #     context: ../admin-app
  #   command: bundle exec sidekiq
  #   volumes:
  #     - ../admin-app:/ezugi
  #   env_file: .env
  #   networks:
  #     - ezugi-main-platform

  # kibana:
  #   image: docker.elastic.co/kibana/kibana:7.10.2
  #   ports:
  #     - "5601:5601"
  #   environment:
  #     - ELASTICSEARCH_USERNAME=elastic
  #     - ELASTICSEARCH_PASSWORD=elastic__tredew
  #   networks:
  #     - ezugi-main-platform
  # settlement-handler:
  #   build:
  #     context: ../sb-bet-settlement-cron
  #     target: builder
  #   command: [ "npm", "run", "start:dev" ]
  #   volumes:
  #     - /home/<USER>/app/node_modules/
  #     - ../sb-bet-settlement-cron:/home/<USER>/app/
  #   ports:
  #     - "8006:8080"
  #   env_file: .env
  #   depends_on:
  #     - database
  #     - elasticsearch
  #     - redis-database
  #   networks:
  #     - ezugi-main-platform
