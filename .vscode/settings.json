{"material-icon-theme.showUpdateMessage": false, "window.zoomLevel": 0, "workbench.iconTheme": "material-icon-theme", "workbench.statusBar.visible": true, "workbench.sideBar.location": "left", "files.autoSave": "onFocusChange", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "editor.tabSize": 2, "editor.renderWhitespace": "all", "editor.minimap.enabled": true, "editor.renderControlCharacters": false, "editor.renderFinalNewline": "on", "editor.rulers": [120], "editor.multiCursorModifier": "ctrlCmd", "editor.autoIndent": "full", "editor.detectIndentation": false, "editor.formatOnSave": true, "breadcrumbs.enabled": true, "cSpell.words": ["DAPP", "GRAPHQL", "Oracleship", "<PERSON><PERSON>", "actioncable", "appbar", "arial", "b<PERSON><PERSON><PERSON>", "baccaart img", "backoff", "balham", "betcol", "bigint", "bin", "block", "btns", "clearfix", "compose", "contracts", "countup", "d", "dailycontest", "dailytask", "dataloader", "deployer", "devtools", "downs", "drop", "easytimer", "extension", "fiftyx", "fivex", "formik", "fullhost", "gcloud", "get", "get drop downs navs", "haspopup", "highcharts", "img", "irant", "klass", "kubectl", "kubernetes", "latestgame", "latestgame block", "lato", "leaderboard", "logo", "logo irant", "max", "menu", "mixins", "modules", "navs", "node", "noopener", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ui<PERSON><PERSON><PERSON>", "numericality", "prng", "progressbar", "rangeslider", "rc", "rc texty", "react", "react rangeslider", "redux", "refetch", "returnvalue", "rmgtrc", "roboto", "romdrops", "romtrc", "rotate", "scatterjs", "scrollator", "sol", "solc", "solhint", "solium", "spreter", "table", "tabpanel", "texty", "todos", "tronbox", "trongrid", "tronlink", "tronpay", "tronweb", "twox", "unmount", "upsert", "uuidv", "valuenow", "warnings", "webfontloader", "yellow", "yellow rotate btns"]}