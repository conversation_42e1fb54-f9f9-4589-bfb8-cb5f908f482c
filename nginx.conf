# server {
#     listen       80;
#     server_name  e-zugi.com www.superadmin.testdomain.com;

#     location / {
#         proxy_pass http://admin-app:8080/;
#         proxy_set_header Host $host;
#     }
# }

server {
    listen       80;
   server_name  pggammastack.com;

    location / {
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_http_version 1.1;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        proxy_pass http://user-frontend:8080;
        proxy_set_header Host $host;
    }
}

# server {
#     listen       80;
#     server_name  admin.ezugis.com admin.ezugislot.com www.admin.testdomain.com;

#     location / {
#         proxy_set_header Upgrade $http_upgrade;
#         proxy_set_header Connection "upgrade";
#         proxy_http_version 1.1;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

#         proxy_pass http://admin-app:8080;
#         proxy_set_header Host $host;
#     }
# }

server {
    listen       80;
   server_name  api.pggammastack.com;

    location / {
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_http_version 1.1;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        proxy_pass http://user-backend:8080;
        proxy_set_header Host $host;
    }
}

# server {
#     listen       80;
#     server_name  settlement.ezugis.com;

#     location / {
#         proxy_set_header Upgrade $http_upgrade;
#         proxy_set_header Connection "upgrade";
#         proxy_http_version 1.1;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

#         proxy_pass http://settlement-handler:8080;
#         proxy_set_header Host $host;
#     }
# }
